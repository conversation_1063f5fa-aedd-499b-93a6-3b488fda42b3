branch="$(git rev-parse --abbrev-ref HEAD)"

if [ "$branch" = "main" ]; then
  echo "You can't push directly to main - please check out a branch."
  exit 1
fi

# Detect if running on Windows and use pnpm.cmd, otherwise use pnpm.
if [ "$OS" = "Windows_NT" ]; then
  pnpm_cmd="pnpm.cmd"
else
  if command -v pnpm >/dev/null 2>&1; then
    pnpm_cmd="pnpm"
  else
    pnpm_cmd="npx pnpm"
  fi
fi

$pnpm_cmd run check-types

# Check for new changesets.
NEW_CHANGESETS=$(find .changeset -name "*.md" ! -name "README.md" | wc -l | tr -d ' ')
echo "Changeset files: $NEW_CHANGESETS"

if [ "$NEW_CHANGESETS" = "0" ]; then
  echo "-------------------------------------------------------------------------------------"
  echo "Changes detected. Please run 'pnpm changeset' to create a changeset if applicable."
  echo "-------------------------------------------------------------------------------------"
fi
