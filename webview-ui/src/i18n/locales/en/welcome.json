{"greeting": "Hi, I'm <PERSON>nT<PERSON><PERSON>!", "introduction": "<strong><PERSON>n<PERSON><PERSON><PERSON> is the an AI agent.</strong> Get ready to boost your productivity like you've never seen before. To continue, NeonTractor requires an API key.", "notice": "To get started, this extension needs an API provider.", "start": "Let's go!", "chooseProvider": "Choose an API provider to get started:", "routers": {"requesty": {"description": "Your optimized LLM router", "incentive": "$1 free credit"}, "openrouter": {"description": "A unified interface for LLMs"}}, "startRouter": "Express Setup Through a Router", "startCustom": "Bring Your Own API Key", "telemetry": {"title": "Help Improve NeonTractor", "anonymousTelemetry": "Send anonymous error and usage data to help us fix bugs and improve the extension. No code, prompts, or personal information is ever sent.", "changeSettings": "You can always change this at the bottom of the <settingsLink>settings</settingsLink>", "settings": "settings", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "or": "or", "importSettings": "Import Settings"}