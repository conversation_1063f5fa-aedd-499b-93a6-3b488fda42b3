{"title": "MCP 服务器", "done": "完成", "description": "<0>Model Context Protocol</0> 支持与本地MCP服务通信，提供扩展功能。您可以使用<1>社区服务器</1>，或通过指令创建定制工具（例如：\"新增获取最新npm文档的工具\"）。", "instructions": "使用说明", "enableToggle": {"title": "启用 MCP 服务器", "description": "开启后 NeonTractor 可用已连接 MCP 服务器的工具，能力更强。不用这些工具时建议关闭，节省 API Token 费用。"}, "enableServerCreation": {"title": "启用 MCP 服务器创建", "description": "开启后 NeonTractor 可帮你创建<1>新</1>自定义 MCP 服务器。<0>了解服务器创建</0>", "hint": "提示：不需要 NeonTractor 创建新 MCP 服务器时建议关闭，减少 API Token 费用。"}, "gateway": {"title": "MCP 网关配置", "description": "配置 MCP 网关连接以增强服务器管理功能", "enable": {"title": "启用 MCP 网关", "description": "开启此选项以启用 MCP 网关集成。这将提供额外的文件处理工具，如 OCR 功能。"}, "url": {"label": "网关 URL", "placeholder": "https://your-mcp-gateway.com/api"}, "apiKey": {"label": "API 密钥", "placeholder": "请输入您的 MCP 网关 API 密钥"}}, "editGlobalMCP": "编辑全局 MCP", "editProjectMCP": "编辑项目 MCP", "learnMoreEditingSettings": "了解如何编辑 MCP 设置文件", "tool": {"alwaysAllow": "始终允许", "parameters": "参数", "noDescription": "无描述"}, "tabs": {"tools": "工具", "resources": "资源", "errors": "错误"}, "emptyState": {"noTools": "未找到工具", "noResources": "未找到资源", "noErrors": "未找到错误"}, "networkTimeout": {"label": "网络超时", "description": "服务器响应最大等待时间", "options": {"15seconds": "15秒", "30seconds": "30秒", "1minute": "1分钟", "5minutes": "5分钟", "10minutes": "10分钟", "15minutes": "15分钟", "30minutes": "30分钟", "60minutes": "60分钟"}}, "deleteDialog": {"title": "删除 MCP 服务器", "description": "确认删除 MCP 服务器 \"{{serverName}}\"？此操作不可逆。", "cancel": "取消", "delete": "删除"}, "serverStatus": {"retrying": "重试中...", "retryConnection": "重试连接"}, "refreshMCP": "刷新 MCP 服务器", "execution": {"running": "运行中", "completed": "已完成", "error": "错误"}}