{"name": "@roo-code/telemetry", "description": "Roo Code telemetry service and clients.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@roo-code/types": "workspace:^", "posthog-node": "^4.7.0", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "20.x", "@types/vscode": "^1.84.0", "vitest": "^3.1.3"}}