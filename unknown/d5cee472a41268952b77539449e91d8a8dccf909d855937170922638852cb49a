// See https://go.microsoft.com/fwlink/?LinkId=733558
// for the documentation about the tasks.json format
{
	"version": "2.0.0",
	"tasks": [
		{
			"label": "watch",
			"dependsOn": ["watch:webview", "watch:bundle", "watch:tsc"],
			"presentation": {
				"reveal": "never"
			},
			"group": {
				"kind": "build",
				"isDefault": true
			}
		},
		{
			"label": "watch:webview",
			"type": "shell",
			"command": "pnpm --filter @roo-code/vscode-webview dev",
			"group": "build",
			"problemMatcher": {
				"owner": "vite",
				"pattern": {
					"regexp": "^$"
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": ".*VITE.*",
					"endsPattern": ".*Local:.*"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "watch:bundle",
			"type": "shell",
			"command": "npx turbo watch:bundle",
			"group": "build",
			"problemMatcher": {
				"owner": "esbuild",
				"pattern": {
					"regexp": "^$"
				},
				"background": {
					"activeOnStart": true,
					"beginsPattern": "esbuild-problem-matcher#onStart",
					"endsPattern": "esbuild-problem-matcher#onEnd"
				}
			},
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		},
		{
			"label": "watch:tsc",
			"type": "shell",
			"command": "npx turbo watch:tsc",
			"group": "build",
			"problemMatcher": "$tsc-watch",
			"isBackground": true,
			"presentation": {
				"group": "watch",
				"reveal": "always"
			}
		}
	]
}
