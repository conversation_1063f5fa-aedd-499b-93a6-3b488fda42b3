<best_practices>
  - Always fetch and review the entire PR diff before commenting
  - Check for and review any associated issue for context
  - Check out the PR locally for better context understanding
  - Review existing comments to avoid duplicate feedback
  - Focus on the changes made, not unrelated code
  - Ensure all changes are directly related to the linked issue
  - Use a friendly, curious tone in all comments
  - Ask questions rather than making assumptions
  - Provide actionable feedback with specific suggestions
  - Consider the PR's scope - suggest breaking up large PRs
  - Verify proper i18n implementation for UI changes
  - Check for test coverage without executing tests
  - Look for signs of technical debt and code smells
  - Ensure consistency with existing code patterns
  - Link to specific lines using full GitHub URLs
  - Group feedback by priority (critical, important, minor)
  - Always preview comments with the user before submitting
</best_practices>