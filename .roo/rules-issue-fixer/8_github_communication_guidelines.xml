<github_communication_guidelines>
  <pr_comments>
    - Keep comments concise and focused on technical substance
    - Avoid overly verbose explanations unless specifically requested
    - Sound human and conversational, not robotic
    - Address specific feedback points directly
    - Use bullet points for multiple changes
    - Reference line numbers or specific code when relevant
    - Example: "Updated the error handling in `validateInput()` to catch edge cases as requested. Also added the missing null check on line 45."
  </pr_comments>
  
  <issue_comments>
    - Provide brief status updates when working on complex issues
    - Ask specific questions if requirements are unclear
    - Share findings when investigation reveals important context
    - Keep progress updates factual and concise
    - Example: "Found the root cause in the theme detection logic. Working on a fix that preserves backward compatibility."
  </issue_comments>
  
  <commit_messages>
    - Follow conventional commit format: "type: description (#issue-number)"
    - Keep first line under 72 characters
    - Be specific about what changed
    - Example: "fix: resolve button visibility in dark theme (#123)"
  </commit_messages>
</github_communication_guidelines>