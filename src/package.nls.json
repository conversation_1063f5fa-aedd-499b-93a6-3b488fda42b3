{"extension.displayName": "NeonTractor", "extension.description": "A whole dev team of AI agents in your editor.", "views.contextMenu.label": "NeonTractor", "views.terminalMenu.label": "NeonTractor", "views.activitybar.title": "Neon Tractor", "views.sidebar.name": "NeonTractor", "command.newTask.title": "New Task", "command.mcpServers.title": "MCP Servers", "command.prompts.title": "Modes", "command.history.title": "History", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Open in Editor", "command.settings.title": "Settings", "command.documentation.title": "Documentation", "command.openInNewTab.title": "Open In New Tab", "command.explainCode.title": "Explain Code", "command.fixCode.title": "Fix Code", "command.improveCode.title": "Improve Code", "command.addToContext.title": "Add To Context", "command.focusInput.title": "Focus Input Field", "command.setCustomStoragePath.title": "Set Custom Storage Path", "command.terminal.addToContext.title": "Add Terminal Content to Context", "command.terminal.fixCommand.title": "Fix This Command", "command.terminal.explainCommand.title": "Explain This Command", "command.acceptInput.title": "Accept Input/Suggestion", "configuration.title": "NeonTractor", "commands.allowedCommands.description": "Commands that can be auto-executed when 'Always approve execute operations' is enabled", "settings.vsCodeLmModelSelector.description": "Settings for VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "The vendor of the language model (e.g. copilot)", "settings.vsCodeLmModelSelector.family.description": "The family of the language model (e.g. gpt-4)", "settings.customStoragePath.description": "Custom storage path. Leave empty to use the default location. Supports absolute paths (e.g. 'D:\\NeonTractorStorage')", "settings.rooCodeCloudEnabled.description": "Enable NeonTractor Cloud."}