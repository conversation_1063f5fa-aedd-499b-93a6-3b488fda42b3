import { InMemoryFileCoolServer } from "../InMemoryMcpServer.js"
import axios from "axios"

// Mock axios
jest.mock("axios")
const mockedAxios = axios as jest.Mocked<typeof axios>

// Mock processFiles
jest.mock("../../file-cool/client.js", () => ({
	processFiles: jest.fn().mockResolvedValue("success")
}))

describe("InMemoryFileCoolServer", () => {
	beforeEach(() => {
		jest.clearAllMocks()
	})

	describe("setupServer with dynamic tools", () => {
		it("should fetch tools list from API and register them", async () => {
			// Mock API response
			const mockTools = [
				{ name: "paddle_ocr", description: "OCR tool" },
				{ name: "image_enhance", description: "Image enhancement tool" },
				{ name: "pdf_convert", description: "PDF conversion tool" }
			]

			mockedAxios.get.mockResolvedValueOnce({
				data: mockTools
			})

			const config = {
				apiUrl: "http://localhost:3000/mcp/",
				apiKey: "test-api-key"
			}

			const server = new InMemoryFileCoolServer(config)

			// Wait for async initialization
			await new Promise(resolve => setTimeout(resolve, 100))

			// Verify API was called correctly
			expect(mockedAxios.get).toHaveBeenCalledWith(
				"http://localhost:3000/mcp/tools",
				{
					headers: {
						"API_KEY": "test-api-key"
					},
					timeout: 10000
				}
			)
		})

		it("should register default tool when API request fails", async () => {
			// Mock API failure
			mockedAxios.get.mockRejectedValueOnce(new Error("Network error"))

			const config = {
				apiUrl: "http://localhost:3000/mcp/",
				apiKey: "test-api-key"
			}

			const server = new InMemoryFileCoolServer(config)

			// Wait for async initialization
			await new Promise(resolve => setTimeout(resolve, 100))

			// Should still create server successfully with default tool
			expect(server).toBeDefined()
		})

		it("should register default tool when config is missing", async () => {
			const server = new InMemoryFileCoolServer()

			// Wait for async initialization
			await new Promise(resolve => setTimeout(resolve, 100))

			// Should create server with default tool when no config provided
			expect(server).toBeDefined()
			expect(mockedAxios.get).not.toHaveBeenCalled()
		})

		it("should handle invalid API response format", async () => {
			// Mock invalid API response
			mockedAxios.get.mockResolvedValueOnce({
				data: "invalid response"
			})

			const config = {
				apiUrl: "http://localhost:3000/mcp/",
				apiKey: "test-api-key"
			}

			const server = new InMemoryFileCoolServer(config)

			// Wait for async initialization
			await new Promise(resolve => setTimeout(resolve, 100))

			// Should fallback to default tool
			expect(server).toBeDefined()
		})
	})

	describe("fetchToolsList", () => {
		it("should throw error when apiUrl is missing", async () => {
			const server = new InMemoryFileCoolServer({ apiKey: "test-key" })
			
			// Access private method for testing
			const fetchToolsList = (server as any).fetchToolsList.bind(server)
			
			await expect(fetchToolsList()).rejects.toThrow("API URL is required to fetch tools list")
		})

		it("should throw error when apiKey is missing", async () => {
			const server = new InMemoryFileCoolServer({ apiUrl: "http://localhost:3000/mcp/" })
			
			// Access private method for testing
			const fetchToolsList = (server as any).fetchToolsList.bind(server)
			
			await expect(fetchToolsList()).rejects.toThrow("API Key is required to fetch tools list")
		})
	})
})
