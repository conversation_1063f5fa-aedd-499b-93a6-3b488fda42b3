[English](../../CONTRIBUTING.md) • [<PERSON><PERSON><PERSON>](../ca/CONTRIBUTING.md) • [<PERSON><PERSON><PERSON>](../de/CONTRIBUTING.md) • [<PERSON><PERSON>a<PERSON><PERSON>](../es/CONTRIBUTING.md) • [Français](../fr/CONTRIBUTING.md) • <b>हिंदी</b> • [Italiano](../it/CONTRIBUTING.md) • [Nederlands](../nl/CONTRIBUTING.md) • [Русский](../ru/CONTRIBUTING.md)

[日本語](../ja/CONTRIBUTING.md) • [한국어](../ko/CONTRIBUTING.md) • [<PERSON>ski](../pl/CONTRIBUTING.md) • [Português (BR)](../pt-BR/CONTRIBUTING.md) • [Türkçe](../tr/CONTRIBUTING.md) • [Tiếng Việt](../vi/CONTRIBUTING.md) • [简体中文](../zh-CN/CONTRIBUTING.md) • [繁體中文](../zh-TW/CONTRIBUTING.md)

# Roo Code में योगदान करें

Roo Code एक समुदाय-आधारित प्रोजेक्ट है और हम हर योगदान को बहुत महत्व देते हैं। सहयोग को सरल बनाने के लिए, हम [Issue-First](#issue-first-एप्रोच) पद्धति अपनाते हैं, जिसका अर्थ है कि सभी [Pull Requests (PRs)](#pull-request-सबमिट-करना) को पहले GitHub Issue से जोड़ना आवश्यक है। कृपया इस गाइड को ध्यान से पढ़ें।

## विषय सूची

- [योगदान करने से पहले](#योगदान-करने-से-पहले)
- [अपना योगदान ढूंढना और योजना बनाना](#अपना-योगदान-ढूंढना-और-योजना-बनाना)
- [विकास और सबमिशन प्रक्रिया](#विकास-और-सबमिशन-प्रक्रिया)
- [कानूनी](#कानूनी)

## योगदान करने से पहले

### 1. आचार संहिता

सभी योगदानकर्ताओं को हमारी [आचार संहिता](./CODE_OF_CONDUCT.md) का पालन करना चाहिए।

### 2. प्रोजेक्ट रोडमैप

हमारा रोडमैप प्रोजेक्ट की दिशा तय करता है। अपने योगदान को इन प्रमुख लक्ष्यों के साथ संरेखित करें:

### विश्वसनीयता पहले

- सुनिश्चित करें कि diff एडिटिंग और कमांड एक्जीक्यूशन लगातार विश्वसनीय हों
- नियमित उपयोग को हतोत्साहित करने वाले फ्रिक्शन पॉइंट्स को कम करें
- सभी भाषाओं और प्लेटफॉर्म्स पर सुचारू संचालन की गारंटी दें
- विभिन्न AI प्रदाताओं और मॉडल्स के लिए मजबूत समर्थन का विस्तार करें

### बेहतर उपयोगकर्ता अनुभव

- स्पष्टता और सहजता के लिए UI/UX को सरल बनाएं
- डेवलपर्स के उच्च अपेक्षाओं को पूरा करने के लिए वर्कफ़्लो में निरंतर सुधार करें

### एजेंट प्रदर्शन में अग्रणी

- वास्तविक दुनिया की उत्पादकता को मापने के लिए व्यापक मूल्यांकन बेंचमार्क (evals) स्थापित करें
- हर किसी के लिए इन मूल्यांकनों को आसानी से चलाना और समझना संभव बनाएं
- ऐसे सुधार लाएं जो मूल्यांकन स्कोर में स्पष्ट वृद्धि दिखाएं

अपने PR में इन क्षेत्रों से संबंधित कार्य का उल्लेख करें।

### 3. Roo Code कम्युनिटी से जुड़ें

- **मुख्य तरीका:** हमारे [Discord](https://discord.gg/roocode) से जुड़ें और **Hannes Rudolph (`hrudolph`)** को DM भेजें।
- **विकल्प:** अनुभवी योगदानकर्ता [GitHub Projects](https://github.com/orgs/RooCodeInc/projects/1) के माध्यम से सीधे भाग ले सकते हैं।

## अपना योगदान ढूंढना और योजना बनाना

### योगदान के प्रकार

- **बग फिक्स:** कोड की समस्याओं को हल करना।
- **नई विशेषताएं:** नई कार्यक्षमता जोड़ना।
- **डॉक्युमेंटेशन:** गाइड सुधारना और स्पष्टता बढ़ाना।

### Issue-First एप्रोच

हर योगदान GitHub Issue से शुरू होना चाहिए।

- **मौजूदा Issues देखें:** [GitHub Issues](https://github.com/RooCodeInc/Roo-Code/issues) में खोजें।
- **Issue बनाएं:** उपयुक्त टेम्पलेट का उपयोग करें:
    - **बग:** "Bug Report" टेम्पलेट।
    - **फीचर्स:** "Detailed Feature Proposal" टेम्पलेट। शुरू करने से पहले अनुमोदन आवश्यक है।
- **Issue क्लेम करें:** कमेंट करें और आधिकारिक असाइनमेंट का इंतजार करें।

**अनुमोदित Issue के बिना PR बंद किए जा सकते हैं।**

### क्या काम करें चुनना

- [GitHub प्रोजेक्ट](https://github.com/orgs/RooCodeInc/projects/1) में असाइन न किए गए "Good First Issues" देखें।
- डॉक्युमेंटेशन के लिए, [Roo Code Docs](https://github.com/RooCodeInc/Roo-Code-Docs) देखें।

### बग या समस्या रिपोर्ट करना

- पहले मौजूदा रिपोर्ट देखें।
- ["Bug Report" टेम्पलेट](https://github.com/RooCodeInc/Roo-Code/issues/new/choose) का उपयोग करके नए बग रिपोर्ट बनाएं।
- **सुरक्षा कमजोरियां:** [security advisories](https://github.com/RooCodeInc/Roo-Code/security/advisories/new) के माध्यम से निजी तौर पर रिपोर्ट करें।

## विकास और सबमिशन प्रक्रिया

### विकास सेटअप

1. **Fork & Clone:**

```
git clone https://github.com/आपका_यूज़रनेम/Roo-Code.git
```

2. **डिपेंडेंसी इंस्टॉल करें:**

```
npm run install:all
```

3. **डिबगिंग:** VS Code में `F5` दबाएं।

### कोड लिखने के दिशा-निर्देश

- प्रति फीचर या फिक्स एक फोकस्ड PR।
- ESLint और TypeScript बेस्ट प्रैक्टिस का पालन करें।
- स्पष्ट, वर्णनात्मक कमिट मैसेज लिखें जो Issues को रेफर करें (जैसे `Fixes #123`)।
- पूर्ण टेस्टिंग प्रदान करें (`npm test`)।
- सबमिट करने से पहले अपनी ब्रांच को नवीनतम `main` पर रीबेस करें।

### Pull Request सबमिट करना

- अगर आप शुरुआती फीडबैक चाहते हैं तो **ड्राफ्ट PR** से शुरू करें।
- Pull Request टेम्पलेट का पालन करते हुए अपने परिवर्तनों का स्पष्ट वर्णन करें।
- UI परिवर्तनों के लिए स्क्रीनशॉट/वीडियो प्रदान करें।
- बताएं कि क्या डॉक्युमेंटेशन अपडेट आवश्यक हैं।

### Pull Request नीति

- पूर्व-अनुमोदित और असाइन किए गए Issues का संदर्भ देना चाहिए।
- नीति का पालन न करने वाले PR बंद किए जा सकते हैं।
- PR को CI टेस्ट पास करना चाहिए, रोडमैप से मेल खाना चाहिए, और स्पष्ट डॉक्युमेंटेशन होनी चाहिए।

### समीक्षा प्रक्रिया

- **दैनिक ट्रायज:** मेंटेनर्स द्वारा त्वरित जांच।
- **साप्ताहिक गहन समीक्षा:** व्यापक मूल्यांकन।
- **फीडबैक के आधार पर तेजी से सुधार** करें।

## कानूनी

Pull Request सबमिट करके, आप सहमत होते हैं कि आपके योगदान Roo Code के लाइसेंसिंग के अनुरूप Apache 2.0 लाइसेंस के तहत लाइसेंस किए जाएंगे।
